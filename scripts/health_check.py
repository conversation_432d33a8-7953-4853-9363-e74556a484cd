#!/usr/bin/env python3
"""
System Health Check Script

Verifies all components of the self-training LLM system are working correctly
and identifies any dependency conflicts or issues.
"""

import sys
import subprocess
import importlib
import traceback
from pathlib import Path
from typing import Dict, List, Tuple, Any
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

console = Console()


class HealthChecker:
    """Comprehensive health checker for the LLM system."""
    
    def __init__(self):
        self.results = {}
        self.errors = []
        self.warnings = []
    
    def run_all_checks(self) -> Dict[str, Any]:
        """Run all health checks and return results."""
        
        console.print(Panel.fit(
            "[bold blue]Self-Training LLM System Health Check[/bold blue]",
            title="System Diagnostics"
        ))
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            
            # Python environment check
            task = progress.add_task("Checking Python environment...", total=None)
            self.results["python"] = self._check_python_environment()
            
            # Core dependencies check
            progress.update(task, description="Checking core dependencies...")
            self.results["dependencies"] = self._check_core_dependencies()
            
            # LLM components check
            progress.update(task, description="Checking LLM components...")
            self.results["llm_components"] = self._check_llm_components()
            
            # Ollama integration check
            progress.update(task, description="Checking Ollama integration...")
            self.results["ollama"] = self._check_ollama_integration()
            
            # Memory systems check
            progress.update(task, description="Checking memory systems...")
            self.results["memory"] = self._check_memory_systems()
            
            # LangGraph integration check
            progress.update(task, description="Checking LangGraph integration...")
            self.results["langgraph"] = self._check_langgraph_integration()
            
            # File system check
            progress.update(task, description="Checking file system...")
            self.results["filesystem"] = self._check_filesystem()
            
            # Configuration check
            progress.update(task, description="Checking configuration...")
            self.results["config"] = self._check_configuration()
            
            progress.update(task, description="Health check complete!")
        
        # Display results
        self._display_results()
        
        return {
            "results": self.results,
            "errors": self.errors,
            "warnings": self.warnings,
            "overall_health": self._calculate_overall_health()
        }
    
    def _check_python_environment(self) -> Dict[str, Any]:
        """Check Python environment health."""
        try:
            python_version = sys.version_info
            
            # Check Python version
            version_ok = python_version >= (3, 9)
            
            # Check virtual environment
            in_venv = hasattr(sys, 'real_prefix') or (
                hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix
            )
            
            # Check pip
            try:
                import pip
                pip_version = pip.__version__
            except ImportError:
                pip_version = "Not available"
            
            return {
                "status": "healthy" if version_ok else "error",
                "python_version": f"{python_version.major}.{python_version.minor}.{python_version.micro}",
                "version_compatible": version_ok,
                "virtual_environment": in_venv,
                "pip_version": pip_version,
                "executable": sys.executable
            }
            
        except Exception as e:
            self.errors.append(f"Python environment check failed: {e}")
            return {"status": "error", "error": str(e)}
    
    def _check_core_dependencies(self) -> Dict[str, Any]:
        """Check core dependencies and version compatibility."""
        
        core_deps = {
            "torch": ">=2.0.0",
            "transformers": ">=4.36.0",
            "chromadb": ">=0.4.18",
            "sentence_transformers": ">=2.2.2",
            "ollama": ">=0.1.7",
            "fastapi": ">=0.104.0",
            "streamlit": ">=1.28.0",
            "rich": ">=13.7.0",
            "pydantic": ">=2.5.0",
            "numpy": ">=1.24.0",
            "pandas": ">=1.5.0"
        }
        
        results = {}
        conflicts = []
        
        for package, min_version in core_deps.items():
            try:
                module = importlib.import_module(package)
                version = getattr(module, '__version__', 'unknown')
                
                results[package] = {
                    "installed": True,
                    "version": version,
                    "status": "ok"
                }
                
            except ImportError:
                results[package] = {
                    "installed": False,
                    "version": None,
                    "status": "missing"
                }
                self.errors.append(f"Missing dependency: {package}")
            
            except Exception as e:
                results[package] = {
                    "installed": False,
                    "version": None,
                    "status": "error",
                    "error": str(e)
                }
        
        # Check for known conflicts
        if results.get("torch", {}).get("installed") and results.get("transformers", {}).get("installed"):
            try:
                import torch
                import transformers
                # Test basic compatibility
                torch.tensor([1, 2, 3])
                results["torch_transformers_compatibility"] = "ok"
            except Exception as e:
                conflicts.append(f"PyTorch-Transformers conflict: {e}")
        
        return {
            "status": "healthy" if not self.errors else "warning",
            "packages": results,
            "conflicts": conflicts
        }
    
    def _check_llm_components(self) -> Dict[str, Any]:
        """Check LLM system components."""
        try:
            # Test core imports
            from core.orchestrator import LLMOrchestrator
            from core.enhanced_orchestrator import EnhancedLLMOrchestrator
            from core.memory.memory_manager import MemoryManager
            from core.learning.learning_engine import LearningEngine
            from models.ollama_client import OllamaClient
            from models.model_router import ModelRouter
            
            return {
                "status": "healthy",
                "core_orchestrator": "ok",
                "enhanced_orchestrator": "ok",
                "memory_manager": "ok",
                "learning_engine": "ok",
                "ollama_client": "ok",
                "model_router": "ok"
            }
            
        except Exception as e:
            self.errors.append(f"LLM components check failed: {e}")
            return {
                "status": "error",
                "error": str(e),
                "traceback": traceback.format_exc()
            }
    
    def _check_ollama_integration(self) -> Dict[str, Any]:
        """Check Ollama integration and model availability."""
        try:
            # Check if Ollama is installed
            result = subprocess.run(["ollama", "--version"], capture_output=True, text=True)
            if result.returncode != 0:
                return {
                    "status": "error",
                    "error": "Ollama not installed or not in PATH"
                }
            
            # Check if Ollama is running
            result = subprocess.run(["ollama", "list"], capture_output=True, text=True)
            if result.returncode != 0:
                return {
                    "status": "error",
                    "error": "Ollama not running"
                }
            
            # Parse available models
            models = []
            for line in result.stdout.split('\\n')[1:]:  # Skip header
                if line.strip():
                    model_name = line.split()[0]
                    models.append(model_name)
            
            # Check required models
            required_models = ["deepseek-r1:32b", "gpt-oss:20b", "qwen3-coder:30b"]
            missing_models = [m for m in required_models if m not in models]
            
            return {
                "status": "healthy" if not missing_models else "warning",
                "ollama_running": True,
                "available_models": models,
                "required_models": required_models,
                "missing_models": missing_models
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    def _check_memory_systems(self) -> Dict[str, Any]:
        """Check memory systems (ChromaDB, Neo4j, SQLite)."""
        results = {}
        
        # ChromaDB check
        try:
            import chromadb
            client = chromadb.Client()
            results["chromadb"] = {
                "status": "ok",
                "version": chromadb.__version__
            }
        except Exception as e:
            results["chromadb"] = {
                "status": "error",
                "error": str(e)
            }
        
        # Neo4j check (optional)
        try:
            from neo4j import GraphDatabase
            results["neo4j"] = {
                "status": "ok",
                "driver_available": True
            }
        except Exception as e:
            results["neo4j"] = {
                "status": "warning",
                "error": "Neo4j driver not available (optional)"
            }
        
        # SQLite check
        try:
            import sqlite3
            conn = sqlite3.connect(":memory:")
            conn.close()
            results["sqlite"] = {
                "status": "ok",
                "version": sqlite3.sqlite_version
            }
        except Exception as e:
            results["sqlite"] = {
                "status": "error",
                "error": str(e)
            }
        
        overall_status = "healthy"
        if any(r.get("status") == "error" for r in results.values()):
            overall_status = "error"
        elif any(r.get("status") == "warning" for r in results.values()):
            overall_status = "warning"
        
        return {
            "status": overall_status,
            "components": results
        }
    
    def _check_langgraph_integration(self) -> Dict[str, Any]:
        """Check LangGraph integration."""
        try:
            from langgraph.graph import StateGraph
            from langgraph.checkpoint.memory import MemorySaver
            from langgraph.prebuilt import ToolNode
            
            # Test basic functionality
            class TestState(dict):
                pass
            
            workflow = StateGraph(TestState)
            workflow.add_node("test", lambda x: x)
            workflow.set_entry_point("test")
            workflow.set_finish_point("test")
            
            return {
                "status": "healthy",
                "state_graph": "ok",
                "memory_saver": "ok",
                "tool_node": "ok",
                "workflow_creation": "ok"
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "traceback": traceback.format_exc()
            }
    
    def _check_filesystem(self) -> Dict[str, Any]:
        """Check file system permissions and structure."""
        try:
            current_dir = Path.cwd()
            
            # Check required directories
            required_dirs = ["core", "models", "config", "web"]
            missing_dirs = [d for d in required_dirs if not (current_dir / d).exists()]
            
            # Check data directory permissions
            data_dir = current_dir / "data"
            data_writable = False
            if data_dir.exists():
                try:
                    test_file = data_dir / "test_write.tmp"
                    test_file.write_text("test")
                    test_file.unlink()
                    data_writable = True
                except:
                    pass
            
            return {
                "status": "healthy" if not missing_dirs else "warning",
                "current_directory": str(current_dir),
                "required_directories": required_dirs,
                "missing_directories": missing_dirs,
                "data_directory_writable": data_writable
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    def _check_configuration(self) -> Dict[str, Any]:
        """Check configuration files."""
        try:
            config_path = Path("config/settings.yaml")
            
            if not config_path.exists():
                return {
                    "status": "error",
                    "error": "Configuration file not found"
                }
            
            import yaml
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            
            # Check required sections
            required_sections = ["models", "memory", "learning", "system"]
            missing_sections = [s for s in required_sections if s not in config]
            
            return {
                "status": "healthy" if not missing_sections else "warning",
                "config_file_exists": True,
                "required_sections": required_sections,
                "missing_sections": missing_sections,
                "config_valid": True
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    def _calculate_overall_health(self) -> str:
        """Calculate overall system health."""
        error_count = len([r for r in self.results.values() if r.get("status") == "error"])
        warning_count = len([r for r in self.results.values() if r.get("status") == "warning"])
        
        if error_count > 0:
            return "unhealthy"
        elif warning_count > 2:
            return "degraded"
        elif warning_count > 0:
            return "healthy_with_warnings"
        else:
            return "healthy"
    
    def _display_results(self):
        """Display health check results."""
        
        # Create summary table
        table = Table(title="Health Check Results")
        table.add_column("Component", style="bold")
        table.add_column("Status")
        table.add_column("Details")
        
        for component, result in self.results.items():
            status = result.get("status", "unknown")
            
            if status == "healthy":
                status_display = "[green]✅ Healthy[/green]"
            elif status == "warning":
                status_display = "[yellow]⚠️  Warning[/yellow]"
            elif status == "error":
                status_display = "[red]❌ Error[/red]"
            else:
                status_display = "[dim]❓ Unknown[/dim]"
            
            # Format details
            details = []
            if "error" in result:
                details.append(f"Error: {result['error']}")
            if "python_version" in result:
                details.append(f"Python {result['python_version']}")
            if "available_models" in result:
                details.append(f"{len(result['available_models'])} models")
            
            table.add_row(
                component.replace("_", " ").title(),
                status_display,
                " | ".join(details) if details else "OK"
            )
        
        console.print(table)
        
        # Overall health
        overall = self._calculate_overall_health()
        if overall == "healthy":
            console.print("\\n[bold green]🎉 System is healthy and ready to use![/bold green]")
        elif overall == "healthy_with_warnings":
            console.print("\\n[bold yellow]⚠️  System is functional but has some warnings.[/bold yellow]")
        elif overall == "degraded":
            console.print("\\n[bold orange]🔧 System has multiple issues that should be addressed.[/bold orange]")
        else:
            console.print("\\n[bold red]🚨 System has critical errors that need immediate attention.[/bold red]")
        
        # Show errors and warnings
        if self.errors:
            console.print("\\n[bold red]Errors:[/bold red]")
            for error in self.errors:
                console.print(f"  • {error}")
        
        if self.warnings:
            console.print("\\n[bold yellow]Warnings:[/bold yellow]")
            for warning in self.warnings:
                console.print(f"  • {warning}")


def main():
    """Main health check function."""
    checker = HealthChecker()
    results = checker.run_all_checks()
    
    # Return appropriate exit code
    overall_health = results["overall_health"]
    if overall_health in ["unhealthy"]:
        sys.exit(1)
    elif overall_health in ["degraded"]:
        sys.exit(2)
    else:
        sys.exit(0)


if __name__ == "__main__":
    main()
