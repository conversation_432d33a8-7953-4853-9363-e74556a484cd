#!/usr/bin/env python3
"""
Working VSCode Cline Integration API
"""

import asyncio
import json
import uuid
from datetime import datetime
from typing import Dict, Any, Optional
import sys

# FastAPI imports
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

class ClineInteraction(BaseModel):
    """Pydantic model for Cline interaction data."""
    user_input: str
    assistant_response: str
    model_used: str
    project_path: Optional[str] = None
    project_name: Optional[str] = None
    conversation_id: Optional[str] = None
    timestamp: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = {}

# Create FastAPI app
app = FastAPI(
    title="Cline Integration API",
    description="API for integrating VSCode Cline with global LLM memory",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """Health check endpoint."""
    return {
        "service": "Cline Integration API",
        "status": "running",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/health")
async def health_check():
    """Detailed health check."""
    return {
        "status": "healthy",
        "integration_enabled": True,
        "timestamp": datetime.now().isoformat()
    }

@app.post("/cline/interaction")
async def receive_cline_interaction(interaction: ClineInteraction):
    """Receive Cline interaction data for processing."""
    try:
        interaction_id = str(uuid.uuid4())
        
        # For now, just acknowledge receipt
        # In full implementation, this would store to global memory
        
        return {
            "status": "accepted",
            "interaction_id": interaction_id,
            "received_at": datetime.now().isoformat(),
            "user_input_length": len(interaction.user_input),
            "response_length": len(interaction.assistant_response)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/cline/stats")
async def get_cline_stats():
    """Get Cline integration statistics."""
    return {
        "integration_enabled": True,
        "timestamp": datetime.now().isoformat(),
        "status": "operational"
    }

def main():
    """Main entry point."""
    print("Starting Cline Integration API server on 127.0.0.1:8765")
    uvicorn.run(app, host="127.0.0.1", port=8765, log_level="info")

if __name__ == "__main__":
    main()
