development:
  debug: false
  hot_reload: false
  mock_models: false
  test_mode: false
finance:
  analysis:
    fundamental:
      enabled: true
      metrics:
      - pe_ratio
      - debt_to_equity
      - roe
      - roa
      - current_ratio
    sentiment:
      enabled: true
      sources:
      - news
      - social_media
      - analyst_reports
    technical:
      enabled: true
      indicators:
      - sma
      - ema
      - rsi
      - macd
      - bollinger_bands
  data_sources:
    alpha_vantage:
      api_key: YOUR_API_KEY
      enabled: false
      rate_limit: 500
    quandl:
      api_key: YOUR_API_KEY
      enabled: false
      rate_limit: 300
    yahoo_finance:
      api_key: null
      enabled: true
      rate_limit: 2000
  strategies:
    arbitrage: true
    mean_reversion: true
    momentum_trading: true
    pattern_recognition: true
    risk_parity: true
integrations:
  terminal:
    auto_context: true
    command_prefix: ai
    enabled: true
    history_size: 1000
  vscode:
    auto_start: true
    enabled: true
    features:
    - code_completion
    - analysis
    - debugging
    port: 8001
  web:
    auth_required: false
    enabled: true
    host: localhost
    port: 8000
learning:
  experience_replay:
    buffer_size: 10000
    enabled: true
    replay_frequency: 100
    sample_size: 32
  fine_tuning:
    batch_size: 4
    enabled: true
    eval_steps: 50
    learning_rate: 0.0001
    max_steps: 1000
    method: lora
    save_steps: 100
    warmup_steps: 100
  lora:
    alpha: 32
    dropout: 0.1
    r: 16
    target_modules:
    - q_proj
    - v_proj
    - k_proj
    - o_proj
  pattern_extraction:
    confidence_threshold: 0.8
    enabled: true
    min_frequency: 5
    update_interval: 1000
memory:
  context:
    auto_summarize: true
    compression_ratio: 0.3
    max_context_length: 8192
    overlap_tokens: 512
  experience_buffer:
    max_experiences: 50000
    path: /Users/<USER>/.llm-system/data/experiences.db
    retention_days: 365
  knowledge_graph:
    database: llm_knowledge
    enabled: false
    password: password
    uri: bolt://localhost:7687
    username: neo4j
  vector_db:
    collection_name: llm_memory
    embedding_model: all-MiniLM-L6-v2
    max_documents: 100000
    path: /Users/<USER>/.llm-system/data/chroma
    similarity_threshold: 0.7
models:
  available:
    deepseek-r1:
      context_length: 32768
      memory_usage: high
      name: deepseek-r1:32b
      specialization:
      - complex_reasoning
      - analysis
      - planning
      type: reasoning
    gpt-oss:
      context_length: 8192
      memory_usage: medium
      name: gpt-oss:20b
      specialization:
      - general_tasks
      - conversation
      - summarization
      type: general
    qwen3-coder:
      context_length: 32768
      memory_usage: high
      name: qwen3-coder:30b
      specialization:
      - code_generation
      - debugging
      - refactoring
      type: coding
  ollama:
    host: localhost
    max_retries: 3
    port: 11434
    timeout: 300
  routing:
    default: deepseek-r1:32b
    tasks:
      code_generation: qwen3-coder:30b
      code_review: qwen3-coder:30b
      conversation: gpt-oss:20b
      debugging: qwen3-coder:30b
      financial_analysis: deepseek-r1:32b
      fundamental_analysis: deepseek-r1:32b
      quick_questions: gpt-oss:20b
      refactoring: qwen3-coder:30b
      risk_assessment: deepseek-r1:32b
      summarization: gpt-oss:20b
      trading_strategy: deepseek-r1:32b
security:
  allowed_origins:
  - http://localhost:3000
  - http://localhost:8080
  api_key_required: false
  cors_enabled: true
  max_requests_per_minute: 100
  rate_limiting: true
system:
  logging:
    backup_count: 5
    file: /Users/<USER>/.llm-system/logs/system.log
    level: INFO
    max_size: 100MB
  monitoring:
    enabled: true
    health_check_interval: 60
    metrics_port: 9090
  performance:
    cpu_limit: 12
    max_concurrent_requests: 10
    memory_limit: 30GB
    request_timeout: 300
  storage:
    auto_backup: true
    backup_interval: 3600
    backup_path: /Users/<USER>/.llm-system/backups
    data_path: /Users/<USER>/.llm-system/data

# VSCode Cline Integration
cline_integration:
  enabled: true
  api_host: "127.0.0.1"
  api_port: 8765
  background_processing: true
  max_queue_size: 1000
  processing_timeout: 30
