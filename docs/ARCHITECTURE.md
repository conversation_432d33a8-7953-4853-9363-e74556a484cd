# Self-Training LLM System Architecture

## System Overview

The self-training LLM system is a hybrid architecture combining LangGraph workflow orchestration with custom self-training capabilities, providing global accessibility, persistent memory, and finance domain specialization.

## Complete System Architecture Diagram

```mermaid
graph TB
    %% User Interfaces
    subgraph "User Interfaces"
        CLI[Terminal CLI<br/>llm-system command]
        VSCode[VSCode Cline<br/>Extension]
        WebUI[Web Dashboard<br/>Streamlit]
    end

    %% Core Orchestration
    subgraph "LangGraph Workflow"
        StateGraph[LangGraph StateGraph<br/>Enhanced Orchestrator]
        AnalyzeInput[analyze_input]
        SelectModel[select_model]
        GetContext[get_context]
        GenerateResponse[generate_response]
        StoreInteraction[store_interaction]
        TriggerLearning[trigger_learning]
    end

    %% Model Layer
    subgraph "Ollama Models"
        DeepSeek[deepseek-r1:32b<br/>Financial Analysis]
        GPTOss[gpt-oss:20b<br/>General Tasks]
        QwenCoder[qwen3-coder:30b<br/>Code Generation]
    end

    %% Memory Systems
    subgraph "Global Memory"
        VectorDB[(ChromaDB<br/>Vector Database)]
        ExperienceDB[(SQLite<br/>Experience Buffer)]
    end

    %% Learning Engine
    subgraph "Self-Training"
        LearningEngine[Learning Engine]
        LoRATrainer[LoRA Trainer]
        PatternExtractor[Pattern Extractor]
    end

    %% Data Flow
    CLI --> StateGraph
    VSCode --> StateGraph
    WebUI --> StateGraph

    StateGraph --> AnalyzeInput
    AnalyzeInput --> SelectModel
    SelectModel --> GetContext
    GetContext --> GenerateResponse
    GenerateResponse --> StoreInteraction
    StoreInteraction --> TriggerLearning

    SelectModel --> DeepSeek
    SelectModel --> GPTOss
    SelectModel --> QwenCoder

    GetContext --> VectorDB
    GetContext --> ExperienceDB
    StoreInteraction --> VectorDB
    StoreInteraction --> ExperienceDB

    TriggerLearning --> LearningEngine
    LearningEngine --> LoRATrainer
    LearningEngine --> PatternExtractor
```

## Key Components

### LangGraph Workflow Orchestration
- **StateGraph**: Manages workflow execution with state persistence
- **Workflow Nodes**: analyze_input → select_model → get_context → generate_response → store_interaction → trigger_learning
- **Error Handling**: Graceful degradation and error routing
- **State Persistence**: Conversation state maintained across sessions

### Intelligent Model Routing
- **deepseek-r1:32b**: Financial analysis, complex reasoning
- **gpt-oss:20b**: General tasks, conversation
- **qwen3-coder:30b**: Code generation, debugging
- **Task-based Selection**: Automatic routing based on content analysis

### Global Memory Systems
- **ChromaDB Vector Database**: Semantic search across all interactions
- **SQLite Experience Buffer**: Structured interaction history
- **Cross-Project Persistence**: Memory shared across all projects

### Self-Training Engine
- **LoRA Fine-tuning**: Model adaptation based on user feedback
- **Pattern Extraction**: Identify successful interaction patterns
- **Experience Replay**: Learn from high-quality past interactions

## Integration Points

### VSCode Cline Integration
- **API Endpoint**: POST /cline/interaction (Port 8765)
- **Background Processing**: Non-blocking queue for Cline data
- **Memory Integration**: Cline conversations stored in global memory

### Performance Characteristics
- **Initialization**: ~3 seconds
- **Model Response**: 15-75 seconds (complexity-dependent)
- **Memory Operations**: <0.5 seconds
- **Learning**: Background processing (non-blocking)

This architecture provides a robust, scalable foundation for self-training LLM capabilities with global accessibility and persistent learning.
