# Global LLM System Directory Structure

## Overview
This document describes the organized directory structure of the global self-training LLM system.

## Directory Layout

```
/Users/<USER>/.llm-system/
├── core/                           # Core system components
│   ├── orchestrator.py            # Original orchestrator
│   ├── enhanced_orchestrator.py   # LangGraph-enhanced orchestrator
│   ├── context/                   # Context management
│   ├── learning/                  # Self-training components
│   └── memory/                    # Memory systems
├── models/                        # Model management
├── web/                          # Web interface
├── config/                       # Configuration
├── data/                         # Persistent data
├── logs/                         # System logs
├── docs/                         # Documentation
├── tests/                        # Test files
├── scripts/                      # Utility scripts
├── venv/                         # Virtual environment
├── llm_cli.py                   # Global CLI interface
└── requirements.txt             # Python dependencies
```

## Clean-up Actions Performed

1. **Moved test files** to `tests/` directory
2. **Moved utility scripts** to `scripts/` directory
3. **Created documentation** structure in `docs/`
4. **Organized configuration** in dedicated `config/` directory
5. **Separated data storage** from application code
6. **Removed redundant files** and temporary artifacts

This structure ensures maximum clarity, maintainability, and extensibility.
