# Self-Training LLM System - User Operations Manual

## Table of Contents
1. [System Overview](#system-overview)
2. [Getting Started](#getting-started)
3. [Basic Operations](#basic-operations)
4. [Advanced Features](#advanced-features)
5. [Integration Guide](#integration-guide)
6. [Monitoring & Maintenance](#monitoring--maintenance)
7. [Troubleshooting](#troubleshooting)

## System Overview

### What is the Self-Training LLM System?
A globally accessible, self-improving AI system that:
- **Learns from every interaction** to provide better responses over time
- **Remembers context across projects** with persistent global memory
- **Specializes in finance** with intelligent domain-specific routing
- **Routes intelligently** between three local Ollama models based on task type
- **Integrates seamlessly** with terminal, VSCode Cline, and web interfaces

### Key Features
- ✅ **Self-Training**: LoRA fine-tuning and experience replay learning
- ✅ **Global Memory**: Cross-project persistent memory with 365-day retention
- ✅ **Finance Specialization**: Domain-specific context and model routing
- ✅ **Intelligent Routing**: Automatic model selection based on task type
- ✅ **Cross-Environment**: Terminal CLI, VSCode integration, web dashboard
- ✅ **LangGraph Orchestration**: Robust workflow management with state persistence

### System Requirements
- **macOS** (tested on macOS with Apple Silicon)
- **Python 3.9+** (managed via dedicated virtual environment)
- **Ollama** with models: deepseek-r1:32b, gpt-oss:20b, qwen3-coder:30b
- **8GB+ RAM** (16GB+ recommended for optimal performance)
- **10GB+ storage** for models and persistent memory

## Getting Started

### Installation Verification
The system is already installed globally. Verify installation:

```bash
# Check if command is available
which llm-system
# Should output: /Users/<USER>/.local/bin/llm-system

# Check system status
llm-system --stats

# Run comprehensive health check
cd /Users/<USER>/.llm-system
source venv/bin/activate
python scripts/health_check.py
# Should show: "🎉 System is healthy and ready to use!"
```

### First Use
```bash
# Simple query from any directory
llm-system --query "Hello, introduce yourself" --project "getting-started"

# Interactive mode
llm-system --interactive --project "my-first-session"

# Check system health
cd /Users/<USER>/.llm-system
source venv/bin/activate
python scripts/health_check.py
```

## Basic Operations

### Command Line Interface

#### Single Query Mode
```bash
# Basic query
llm-system --query "Your question here"

# With project context
llm-system --query "Analyze AAPL stock performance" --project "trading-analysis"

# With specific model preference
llm-system --query "Write Python code for portfolio optimization" --model "qwen3-coder:30b"
```

#### Interactive Mode
```bash
# Start interactive session
llm-system --interactive

# With project context
llm-system --interactive --project "portfolio-management"

# Commands in interactive mode:
# - Type your questions normally
# - 'help' for available commands
# - 'quit', 'exit', or 'q' to exit
```

#### System Information
```bash
# System statistics
llm-system --stats

# Health check (detailed)
cd /Users/<USER>/.llm-system && source venv/bin/activate && python scripts/health_check.py
```

### Project Context Management

#### Automatic Project Detection
When you don't specify `--project`, the system uses the current directory name:
```bash
cd ~/projects/trading-bot
llm-system --query "Help with risk calculation"
# Project context: "trading-bot"

cd ~/projects/portfolio-analysis  
llm-system --query "Analyze portfolio performance"
# Project context: "portfolio-analysis"
```

#### Manual Project Specification
```bash
# Explicit project context
llm-system --query "Your question" --project "my-trading-system"

# Cross-project queries
llm-system --query "Compare strategies from trading-bot project" --project "strategy-comparison"
```

### Model Selection

#### Automatic Model Routing
The system automatically selects the best model based on your query:

- **Financial Analysis** → `deepseek-r1:32b`
  ```bash
  llm-system --query "Analyze Apple's quarterly earnings"
  # Automatically uses deepseek-r1:32b
  ```

- **Code Generation** → `qwen3-coder:30b`
  ```bash
  llm-system --query "Write a Python function to calculate VaR"
  # Automatically uses qwen3-coder:30b
  ```

- **General Questions** → `gpt-oss:20b`
  ```bash
  llm-system --query "Explain machine learning concepts"
  # Automatically uses gpt-oss:20b
  ```

#### Manual Model Selection
```bash
# Force specific model
llm-system --query "Your question" --model "deepseek-r1:32b"
llm-system --query "Your question" --model "gpt-oss:20b"
llm-system --query "Your question" --model "qwen3-coder:30b"
```

## Advanced Features

### Self-Training and Learning

#### How Self-Training Works
1. **Every interaction** is stored in the global memory system
2. **Patterns are extracted** from successful interactions
3. **Models are fine-tuned** using LoRA adaptation
4. **Experience replay** learns from high-quality past interactions
5. **Performance improves** with each use

#### Monitoring Learning Progress
```bash
# Check memory growth
llm-system --stats
# Look for: vector_documents, experiences, learning_triggers

# View detailed memory statistics
cd /Users/<USER>/.llm-system
source venv/bin/activate
python -c "
from core.memory.memory_manager import MemoryManager
import yaml
with open('config/settings.yaml', 'r') as f:
    config = yaml.safe_load(f)
mm = MemoryManager(config['memory'])
print('Memory Stats:', mm.get_stats())
mm.close()
"
```

### Global Memory System

#### Memory Components
1. **Vector Database (ChromaDB)**: Semantic search across all interactions
2. **Experience Buffer (SQLite)**: Structured interaction history
3. **Knowledge Graph (Neo4j)**: Relationship mapping (optional)

#### Memory Persistence
- **Location**: `/Users/<USER>/.llm-system/data/`
- **Retention**: 365 days for experiences, permanent for vector embeddings
- **Backup**: Automatic daily backups to `/Users/<USER>/.llm-system/backups/`

#### Cross-Project Memory Sharing
```bash
# Project A: Learn about risk management
cd ~/projects/risk-modeling
llm-system --query "Explain Value at Risk calculation" --project "risk-modeling"

# Project B: Apply learned knowledge
cd ~/projects/portfolio-optimizer
llm-system --query "Use VaR in portfolio optimization" --project "portfolio-optimizer"
# System remembers VaR explanation from Project A
```

### Finance Domain Specialization

#### Specialized Capabilities
- **Stock Analysis**: Fundamental and technical analysis
- **Portfolio Management**: Optimization, risk assessment, performance analysis
- **Trading Strategies**: Algorithm development, backtesting, risk management
- **Financial Modeling**: Valuation models, scenario analysis, stress testing
- **Risk Management**: VaR, stress testing, correlation analysis

#### Finance-Specific Features
```bash
# Automatic entity recognition
llm-system --query "Analyze AAPL, MSFT, and GOOGL correlation"
# Recognizes: stocks, correlation analysis → routes to deepseek-r1:32b

# Domain-specific context
llm-system --query "Create momentum trading strategy" --project "algo-trading"
# Applies: trading strategy context, risk management principles

# Financial calculations
llm-system --query "Calculate portfolio Sharpe ratio with Python" --project "portfolio-analysis"
# Routes to: qwen3-coder:30b with finance context
```

## Integration Guide

### VSCode Cline Integration

#### Setup (API Integration)
1. **Start Cline Integration API**:
   ```bash
   cd /Users/<USER>/.llm-system
   source venv/bin/activate
   python scripts/cline_integration_api.py
   # API runs on http://127.0.0.1:8765
   ```

2. **Configure VSCode Extension** (when available):
   - API Endpoint: `http://127.0.0.1:8765/cline/interaction`
   - Enable automatic conversation capture
   - Set project path for context

#### Manual Integration (Current)
Send Cline conversations to the global memory:
```bash
curl -X POST http://127.0.0.1:8765/cline/interaction \
  -H "Content-Type: application/json" \
  -d '{
    "user_input": "Your question to Cline",
    "assistant_response": "Cline response",
    "model_used": "deepseek-r1:32b",
    "project_path": "/path/to/your/project",
    "project_name": "your-project-name"
  }'
```

#### Benefits of Integration
- **Unified Memory**: Cline conversations contribute to global learning
- **Cross-Context Learning**: Terminal and VSCode interactions inform each other
- **Persistent Knowledge**: Cline insights available across all environments

### Web Dashboard

#### Starting the Dashboard
```bash
cd /Users/<USER>/.llm-system
source venv/bin/activate
streamlit run web/dashboard.py
# Access at: http://localhost:8501
```

#### Dashboard Features
- **Interactive Chat**: Web-based conversation interface
- **Memory Visualization**: View vector database and experience history
- **Performance Metrics**: Response times, model usage, learning progress
- **System Monitoring**: Health status, memory usage, error logs

### API Integration

#### REST API Endpoints
```bash
# Health check
curl http://127.0.0.1:8765/health

# Submit interaction
curl -X POST http://127.0.0.1:8765/cline/interaction \
  -H "Content-Type: application/json" \
  -d '{"user_input": "test", "assistant_response": "response", "model_used": "deepseek-r1:32b"}'

# Get statistics
curl http://127.0.0.1:8765/cline/stats

# Enable/disable integration
curl -X POST http://127.0.0.1:8765/cline/enable
curl -X POST http://127.0.0.1:8765/cline/disable
```

## Monitoring & Maintenance

### Performance Monitoring

#### System Statistics
```bash
# Quick stats
llm-system --stats

# Detailed performance metrics
cd /Users/<USER>/.llm-system
source venv/bin/activate
python performance_benchmark.py  # If available
```

#### Memory Usage Monitoring
```bash
# Check data directory size
du -sh /Users/<USER>/.llm-system/data/

# Vector database size
du -sh /Users/<USER>/.llm-system/data/chroma/

# Experience buffer size
ls -lh /Users/<USER>/.llm-system/data/experiences.db
```

#### Log Monitoring
```bash
# System logs
tail -f /Users/<USER>/.llm-system/logs/system.log

# Cline integration logs
tail -f /Users/<USER>/.llm-system/logs/cline_integration.log

# Error patterns
grep -i error /Users/<USER>/.llm-system/logs/system.log
```

### Adding New Models

#### Install New Ollama Model
```bash
# Install new model
ollama pull llama3-finance:70b

# Verify installation
ollama list | grep llama3-finance
```

#### Configure Model Routing
1. **Edit configuration**:
   ```bash
   nano /Users/<USER>/.llm-system/config/settings.yaml
   ```

2. **Add model configuration**:
   ```yaml
   models:
     available_models:
       llama3-finance:
         name: "llama3-finance:70b"
         specialization: ["financial_analysis", "fundamental_analysis"]
         context_window: 8192
         max_tokens: 4096
   ```

3. **Test new model**:
   ```bash
   llm-system --query "Test new model" --model "llama3-finance:70b"
   ```

### Backup and Recovery

#### Automatic Backups
- **Location**: `/Users/<USER>/.llm-system/backups/`
- **Frequency**: Daily (if configured)
- **Contents**: Vector database, experience buffer, configuration

#### Manual Backup
```bash
# Create backup
cd /Users/<USER>/.llm-system
tar -czf "backup-$(date +%Y%m%d).tar.gz" data/ config/ logs/

# Restore from backup
tar -xzf backup-********.tar.gz
```

#### Data Recovery
```bash
# Rebuild vector database (if corrupted)
cd /Users/<USER>/.llm-system
source venv/bin/activate
python -c "
from core.memory.vector_store import VectorStore
import yaml
with open('config/settings.yaml', 'r') as f:
    config = yaml.safe_load(f)
vs = VectorStore(config['memory']['vector_db'])
print('Vector store rebuilt')
"
```

## Troubleshooting

### Common Issues

#### Command Not Found
```bash
# Problem: llm-system command not found
# Solution: Check PATH
echo $PATH | grep -o '/Users/<USER>/.local/bin'

# If not found, add to shell profile:
echo 'export PATH="/Users/<USER>/.local/bin:$PATH"' >> ~/.zshrc
source ~/.zshrc
```

#### Ollama Connection Issues
```bash
# Problem: Cannot connect to Ollama
# Check if Ollama is running:
ollama list

# If not running, start Ollama:
ollama serve

# Check available models:
ollama list | grep -E "(deepseek-r1|gpt-oss|qwen3-coder)"
```

#### Memory System Errors
```bash
# Problem: ChromaDB or SQLite errors
# Run health check:
cd /Users/<USER>/.llm-system
source venv/bin/activate
python scripts/health_check.py

# Check data directory permissions:
ls -la /Users/<USER>/.llm-system/data/

# Rebuild if necessary:
rm -rf /Users/<USER>/.llm-system/data/chroma/
# System will rebuild on next use
```

#### Performance Issues
```bash
# Problem: Slow response times
# Check system resources:
top -pid $(pgrep ollama)

# Check model memory usage:
ollama ps

# Restart Ollama if needed:
pkill ollama
ollama serve
```

#### Python Environment Issues
```bash
# Problem: Import errors or dependency conflicts
# Verify virtual environment:
cd /Users/<USER>/.llm-system
source venv/bin/activate
python -c "import sys; print(sys.prefix)"

# Reinstall dependencies if needed:
pip install -r requirements.txt
```

### Error Codes and Solutions

| Error Code | Description | Solution |
|------------|-------------|----------|
| `ModuleNotFoundError` | Missing Python dependencies | `pip install -r requirements.txt` |
| `ConnectionError` | Ollama not accessible | Check `ollama serve` is running |
| `FileNotFoundError` | Missing configuration | Verify `/Users/<USER>/.llm-system/config/settings.yaml` |
| `PermissionError` | Data directory access | Check permissions on `data/` directory |
| `TimeoutError` | Model response timeout | Check model availability with `ollama list` |

### Getting Help

#### System Diagnostics
```bash
# Complete system health check
cd /Users/<USER>/.llm-system
source venv/bin/activate
python scripts/health_check.py

# System information
llm-system --stats

# Log analysis
tail -100 /Users/<USER>/.llm-system/logs/system.log
```

#### Performance Benchmarking
```bash
# Run performance tests
cd /Users/<USER>/.llm-system
source venv/bin/activate
python performance_benchmark.py  # If available
```

#### Configuration Validation
```bash
# Validate configuration
cd /Users/<USER>/.llm-system
source venv/bin/activate
python -c "
import yaml
with open('config/settings.yaml', 'r') as f:
    config = yaml.safe_load(f)
print('Configuration valid')
print('Available models:', list(config['models']['available_models'].keys()))
"
```

## Best Practices

### Effective Usage
1. **Use descriptive project names** for better context organization
2. **Provide specific queries** for better model routing
3. **Monitor memory growth** to ensure optimal performance
4. **Regular health checks** to catch issues early
5. **Backup important conversations** before major system changes

### Performance Optimization
1. **Close unused Ollama models** to free memory
2. **Use appropriate models** for each task type
3. **Monitor system resources** during heavy usage
4. **Clean old logs** periodically to save disk space

### Security Considerations
1. **API access** is currently localhost-only (127.0.0.1)
2. **Data storage** is local and encrypted at rest
3. **No external API calls** except to local Ollama
4. **Conversation data** remains on your machine

This manual provides comprehensive guidance for operating the self-training LLM system effectively. For additional support, refer to the system logs and health check outputs.
