# Core Dependencies
fastapi==0.104.1
uvicorn==0.24.0
pydantic==2.5.0
python-dotenv==1.0.0
pyyaml==6.0.1
click==8.1.7
rich==13.7.0

# LLM and AI
ollama==0.1.7
transformers==4.36.0
torch==2.1.0
accelerate==0.24.0
peft==0.6.0
bitsandbytes==0.41.3
datasets==2.14.0

# LangGraph and <PERSON><PERSON><PERSON><PERSON>
langgraph>=0.6.6
langchain-core>=0.1
langgraph-checkpoint>=2.1.0
langgraph-prebuilt>=0.6.0
langgraph-sdk>=0.2.2
xxhash>=3.5.0

# Memory Systems
chromadb==0.4.18
neo4j==5.28.2
sentence-transformers==2.2.2
faiss-cpu==1.7.4
numpy==1.24.3
pandas==2.1.3

# Data Processing
scikit-learn==1.3.2
scipy==1.11.4
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0

# Finance Libraries
yfinance==0.2.28
alpha-vantage==2.3.1
quandl==3.7.0
pandas-datareader==0.10.0
ta==0.10.2
quantlib==1.32

# Development Tools
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0
mypy==1.7.0
pre-commit==3.5.0

# Monitoring and Logging
mlflow==2.8.1
wandb==0.16.0
loguru==0.7.2
prometheus-client==0.19.0

# Web and API
streamlit==1.28.1
gradio==4.7.1
websockets>=10.0,<12.0
aiofiles==23.2.1

# System Integration
psutil==5.9.6
watchdog==3.0.0
schedule==1.2.0
apscheduler==3.10.4

# Database
sqlalchemy==2.0.23
alembic==1.12.1

# Utilities
tqdm==4.66.1
requests==2.31.0
httpx==0.25.2
tenacity==8.2.3
cachetools==5.3.2
