Metadata-Version: 2.1
Name: Quandl
Version: 3.7.0
Summary: Package for quandl API access
Home-page: https://github.com/quandl/quandl-python
Author: Quandl
Author-email: <EMAIL>
Maintainer: Quandl Development Team
Maintainer-email: <EMAIL>
License: MIT
Keywords: quandl,API,data,financial,economic
Platform: UNKNOWN
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Requires-Python: >= 3.6
License-File: LICENSE.txt
Requires-Dist: pandas (>=0.14)
Requires-Dist: numpy (>=1.8)
Requires-Dist: requests (>=2.7.0)
Requires-Dist: inflection (>=0.3.1)
Requires-Dist: python-dateutil
Requires-Dist: six
Requires-Dist: more-itertools

Official Quandl API Client for Python
=========================================

A Python library for Quandl's RESTful API.


Setup
=====

You can install this package by using the pip tool and installing:

    $ pip install quandl

Or:

    $ easy_install quandl

Usage
=====

You can read more about developing and using the library here: https://github.com/quandl/quandl-python

