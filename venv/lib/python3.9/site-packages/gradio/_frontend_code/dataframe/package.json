{"name": "@gradio/dataframe", "version": "0.3.10", "description": "Gradio UI packages", "type": "module", "author": "", "license": "ISC", "private": false, "main_changeset": true, "main": "./Index.svelte", "dependencies": {"@gradio/atoms": "workspace:^", "@gradio/button": "workspace:^", "@gradio/markdown": "workspace:^", "@gradio/statustracker": "workspace:^", "@gradio/upload": "workspace:^", "@gradio/utils": "workspace:^", "@types/d3-dsv": "^3.0.0", "@types/dompurify": "^3.0.2", "@types/katex": "^0.16.0", "d3-dsv": "^3.0.1", "dequal": "^2.0.2", "dompurify": "^3.0.3", "katex": "^0.16.7", "marked": "^7.0.0"}, "exports": {".": "./Index.svelte", "./example": "./Example.svelte", "./package.json": "./package.json"}}