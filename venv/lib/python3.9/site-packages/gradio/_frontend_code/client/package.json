{"name": "@gradio/client", "version": "0.8.2", "description": "Gradio API client", "type": "module", "main": "dist/index.js", "author": "", "license": "ISC", "exports": {".": {"import": "./dist/index.js"}, "./package.json": "./package.json"}, "dependencies": {"bufferutil": "^4.0.7", "semiver": "^1.1.0", "ws": "^8.13.0"}, "devDependencies": {"@types/ws": "^8.5.4", "esbuild": "^0.19.0"}, "scripts": {"bundle": "vite build --ssr", "generate_types": "tsc", "build": "pnpm bundle && pnpm generate_types"}, "engines": {"node": ">=18.0.0"}, "main_changeset": true}