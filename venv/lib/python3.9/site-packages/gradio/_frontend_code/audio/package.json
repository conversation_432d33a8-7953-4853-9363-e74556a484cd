{"name": "@gradio/audio", "version": "0.5.4", "description": "Gradio UI packages", "type": "module", "author": "", "license": "ISC", "private": false, "dependencies": {"@gradio/atoms": "workspace:^", "@gradio/button": "workspace:^", "@gradio/client": "workspace:^", "@gradio/icons": "workspace:^", "@gradio/statustracker": "workspace:^", "@gradio/upload": "workspace:^", "@gradio/utils": "workspace:^", "@gradio/wasm": "workspace:^", "extendable-media-recorder": "^9.0.0", "extendable-media-recorder-wav-encoder": "^7.0.76", "resize-observer-polyfill": "^1.5.1", "svelte-range-slider-pips": "^2.0.1", "wavesurfer.js": "^7.4.2", "@types/wavesurfer.js": "^6.0.10"}, "main_changeset": true, "main": "index.ts", "exports": {".": "./index.ts", "./example": "./Example.svelte", "./package.json": "./package.json"}}